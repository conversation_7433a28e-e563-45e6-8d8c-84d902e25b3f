import { client } from './client';
import { ChatHistorySummary } from '@/components/features/chathistory';
import { ChatType, chatTypeToUrl } from '@/components/features/chat';
import { formatISOToReadableDateTime } from '@/utils/dateUtils';
import { API_BASE_PATH } from './apiConfig';
import { parseApiError } from './apiUtils';

const HISTORY_BASE_URL = `${API_BASE_PATH}/restful/history_json`;

const backendSessionTypeMap: { [key: string]: ChatType } = {
  chat: 'General',
  code: 'Code',
  mdlm: 'Medical',
};

export function mapBackendSessionType(backendType: string | number | undefined): ChatType {
  if (backendType) {
    switch (typeof backendType) {
      case 'string':
        if (backendSessionTypeMap[backendType]) {
          return backendSessionTypeMap[backendType];
        }
        break;
      case 'number':
        switch (backendType) {
          case 3:
            return backendSessionTypeMap['mdlm'];
          case 2:
            return backendSessionTypeMap['code'];
          case 1:
          default:
            return backendSessionTypeMap['chat'];
        }
    }
  }

  console.warn(`Unknown session type received from backend: ${backendType}. Defaulting to General.`);
  return 'General';
}

export type ConversationTurn = {
  conversation_role: 'user' | 'model';
  created_utc: string;
  updated_utc: string;
  feedback: boolean | null;
  id: string;
  message: string;
  temperature?: number;
};

export interface BackendHistoryItem {
  id: string;
  created_utc: string;
  updated_utc: string;
  session_type: string;
  session_summary: string | null;
  system_instructions: string | null;
  conversation_history: ConversationTurn[];
  file_history: any[];
}

/**
 * Fetches the list of chat history summaries from the backend.
 * Re-throws errors for the caller to handle.
 */
export async function fetchChatHistory(): Promise<ChatHistorySummary[]> {
  try {
    const response = await client.get<BackendHistoryItem[]>(HISTORY_BASE_URL);

    // if (!Array.isArray(response.data)) {
    //   console.error('Invalid chat history response: Data is not an array.', response.data);
    //   throw new Error('Received invalid data format for chat history.');
    // }

    const transformedData = response.data
      .map(item => {
        if (!item || typeof item.id !== 'string' || typeof item.created_utc !== 'string') {
          console.warn('Skipping invalid history item:', item);
          return null;
        }
        // const firstPrompt = item.conversation_history?.[0]?.message || '[No initial prompt found]';
        const formattedDate = formatISOToReadableDateTime(item.created_utc);

        const formattedUpdatedDate = formatISOToReadableDateTime(item.updated_utc);

        if (formattedDate === 'Invalid Date') {
          console.warn('Skipping history item due to invalid date:', item);
          return null;
        }

        // Check if there's a significant difference between created and updated times
        const createdTime = new Date(item.created_utc);
        const updatedTime = new Date(item.updated_utc);
        const timeDifferenceMs = updatedTime.getTime() - createdTime.getTime();
        const timeDifferenceMinute = timeDifferenceMs / (1000 * 60);
        // If updated more than 1 minute after creation, add the updated date
        const createdTitle = timeDifferenceMinute > 1 ? `(created ${formattedDate})` : '';
        const sessionType = mapBackendSessionType(item.session_type);

        return {
          id: item.id,
          title: `Chat from ${formattedUpdatedDate}`,
          createdTitle: createdTitle,
          sessionSummary: item.session_summary,
          timestamp: item.created_utc,
          fullChatUrl: `/${chatTypeToUrl[sessionType]}/${item.id}`,
          sessionType: sessionType,
          updatedTimestamp: item.updated_utc,
        };
      })
      .filter(item => item !== null) as ChatHistorySummary[];

    return transformedData;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error('Failed to fetch chat history:', parsedError.originalError || error);
    throw new Error(`Failed to load chat history: ${parsedError.message}`);
  }
}

/**
 * Fetches the full details for a single chat session.
 */
export async function fetchSessionDetails(sessionId: string): Promise<BackendHistoryItem> {
  const endpoint = `${HISTORY_BASE_URL}/${sessionId}`;
  try {
    const response = await client.get<BackendHistoryItem>(endpoint);
    // Basic validation
    if (!response.data || typeof response.data !== 'object') {
      console.error(
        `Invalid session details response for ID ${sessionId}: Data is missing or not an object.`,
        response.data
      );
      throw new Error('Received invalid data format for session details.');
    }
    if (
      !response.data.id ||
      !response.data.conversation_history ||
      !Array.isArray(response.data.conversation_history)
    ) {
      console.error(
        `Invalid session details for ID ${sessionId}: Missing required fields or invalid conversation history.`,
        response.data
      );
      throw new Error('Received incomplete session details.');
    }
    return response.data;
  } catch (error: unknown) {
    const parsedError = parseApiError(error);
    console.error(`Failed to fetch session details for ID ${sessionId}:`, parsedError.originalError || error);
    throw new Error(`Failed to load session details for ${sessionId}: ${parsedError.message}`);
  }
}
