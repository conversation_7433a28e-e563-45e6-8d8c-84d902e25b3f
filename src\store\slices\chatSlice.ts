import { createSidekickSlice } from '../withTypes';
import { sendChatMessage, ChatInitiateResponse, ChatType } from '@/api/chatApi';
import { v4 as uuidv4 } from 'uuid';
import type { RootState } from '../store';
import { BackendHistoryItem, mapBackendSessionType } from '@/api/chatHistoryApi';
import {
  policyLocationOptions,
  defaultPolicyLocationValue,
  PolicyOptionGroup,
} from '@/components/features/policy_web/config/policyWebOptions';
import { FileInfo } from '@/types/fileUpload';

export interface BackendFilePayloadEntry {
  gcs_path: string;
  mime_type: string;
  file_size: number;
  upload_time_utc: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'model' | 'system';
  text: string;
  timestamp: string;
  feedback?: 0 | 1 | null;
  fileInfo?: FileInfo[];
  isPolicyContext?: boolean;
  policyWebData?: {
    sessionId?: string;
    responseId?: string;
    responseClass?: any;
  };
}

export interface ChatState {
  messages: ChatMessage[];
  selectedChatType: ChatType;
  selectedTemperature: number;
  selectedSystemInstructions: string;
  useCustomInstructions: boolean;
  isLoading: boolean;
  error: string | null;
  currentSessionId: string | null;
  firestoreSessionId: string | null;
  activePollingId: string | null;
  file_history: BackendFilePayloadEntry[];
  policyLocationOptions: PolicyOptionGroup[];
  selectedPolicyLocation: string | null;
}

// Helper functions for localStorage persistence
const CHAT_SETTINGS_STORAGE_KEY = 'sidekick_chat_settings';

interface ChatSettings {
  selectedTemperature: number;
  selectedSystemInstructions: string;
  useCustomInstructions: boolean;
}

const loadChatSettingsFromStorage = (): Partial<ChatSettings> => {
  try {
    const stored = localStorage.getItem(CHAT_SETTINGS_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Validate the stored data
      if (typeof parsed === 'object' && parsed !== null) {
        return {
          selectedTemperature: typeof parsed.selectedTemperature === 'number' ? parsed.selectedTemperature : 1.0,
          selectedSystemInstructions:
            typeof parsed.selectedSystemInstructions === 'string' ? parsed.selectedSystemInstructions : '',
          useCustomInstructions:
            typeof parsed.useCustomInstructions === 'boolean' ? parsed.useCustomInstructions : false,
        };
      }
    }
  } catch (error) {
    console.warn('Failed to load chat settings from localStorage:', error);
  }
  return {};
};

const saveChatSettingsToStorage = (settings: Partial<ChatSettings>) => {
  try {
    const current = loadChatSettingsFromStorage();
    const updated = { ...current, ...settings };
    localStorage.setItem(CHAT_SETTINGS_STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.warn('Failed to save chat settings to localStorage:', error);
  }
};

// Load persisted settings
const persistedSettings = loadChatSettingsFromStorage();

const initialState: ChatState = {
  messages: [],
  selectedChatType: 'General',
  selectedTemperature: persistedSettings.selectedTemperature ?? 1.0,
  selectedSystemInstructions: persistedSettings.selectedSystemInstructions ?? '',
  useCustomInstructions: persistedSettings.useCustomInstructions ?? false,
  isLoading: false,
  error: null,
  currentSessionId: null,
  firestoreSessionId: null,
  activePollingId: null,
  file_history: [],
  policyLocationOptions: policyLocationOptions,
  selectedPolicyLocation: defaultPolicyLocationValue,
};

export interface SendMessageThunkPayload {
  prompt: string;
  files?: FileInfo[];
  promptTemplateId?: string;
  promptTemplateIsGlobal?: boolean;
}

const chatSlice = createSidekickSlice({
  name: 'chat',
  initialState,

  reducers: create => ({
    setSelectedChatType: create.reducer<ChatType>((state, action) => {
      state.selectedChatType = action.payload;
      state.isLoading = false;
      state.messages = [];
      state.currentSessionId = null;
      state.firestoreSessionId = null;
      state.activePollingId = null;
      state.error = null;
      state.selectedPolicyLocation = defaultPolicyLocationValue;
      // Reset system instructions when switching chat types
      state.selectedSystemInstructions = '';
      state.useCustomInstructions = false;
    }),
    setSelectedTemperature: create.reducer<number>((state, action) => {
      const newTemp = action.payload;
      if ([0.2, 1.0, 1.8].includes(newTemp)) {
        state.selectedTemperature = newTemp;
        // Persist to localStorage
        saveChatSettingsToStorage({ selectedTemperature: newTemp });
      } else {
        console.warn(`Invalid temperature value received: ${newTemp}. Keeping ${state.selectedTemperature}.`);
      }
    }),
    setSelectedSystemInstructions: create.reducer<string>((state, action) => {
      state.selectedSystemInstructions = action.payload;
      // Persist to localStorage
      saveChatSettingsToStorage({ selectedSystemInstructions: action.payload });
    }),
    setUseCustomInstructions: create.reducer<boolean>((state, action) => {
      state.useCustomInstructions = action.payload;
      // Persist to localStorage
      saveChatSettingsToStorage({ useCustomInstructions: action.payload });
    }),
    setSelectedPolicyLocation: create.reducer<string | null>((state, action) => {
      state.selectedPolicyLocation = action.payload;
    }),
    addMessage: create.reducer<ChatMessage>((state, action) => {
      const lastMessage = state.messages[state.messages.length - 1];
      if (
        action.payload.role === 'system' &&
        lastMessage?.role === 'system' &&
        lastMessage?.text === action.payload.text &&
        !action.payload.fileInfo
      ) {
        return;
      }
      state.messages.push(action.payload);
      if (action.payload.role === 'model') {
        state.isLoading = false;
        state.activePollingId = null;
      }
      state.file_history = [];
      if (state.selectedChatType !== 'Policy') {
        state.selectedPolicyLocation = defaultPolicyLocationValue;
      }
    }),
    setChatIsLoading: create.reducer<boolean>((state, action) => {
      state.isLoading = action.payload;
      if (!action.payload) {
        state.activePollingId = null;
      }
    }),
    setError: create.reducer<string | null>((state, action) => {
      state.error = action.payload;
      state.isLoading = false;
      state.activePollingId = null;
    }),
    clearError: create.reducer(state => {
      state.error = null;
    }),
    setSessionIds: create.reducer<{ sessionId?: string | null; firestoreId?: string | null }>((state, action) => {
      if (action.payload.sessionId !== undefined) state.currentSessionId = action.payload.sessionId;
      if (action.payload.firestoreId !== undefined) state.firestoreSessionId = action.payload.firestoreId;
    }),

    setMessageFeedback: create.reducer<{ messageId: string; feedback: 0 | 1 | null }>((state, action) => {
      const { messageId, feedback } = action.payload;
      const messageIndex = state.messages.findIndex(msg => msg.id === messageId);
      if (messageIndex !== -1) {
        state.messages[messageIndex].feedback = feedback;
      }
    }),

    resumeChatSession: create.reducer<{ sessionData: BackendHistoryItem; messages: ChatMessage[] }>((state, action) => {
      state.isLoading = false;
      state.activePollingId = null;
      const { sessionData, messages } = action.payload;
      const currentChatType = mapBackendSessionType(sessionData.session_type);
      state.messages = messages;
      state.currentSessionId = sessionData.id;
      state.firestoreSessionId = sessionData.id;
      state.file_history = (sessionData.file_history || []).map(file => ({
        gcs_path: file.gcs_path || file.gcs_uri,
        mime_type: file.mime_type,
        file_size: file.file_size,
        upload_time_utc: file.upload_time_utc || new Date().toISOString(),
      })) as BackendFilePayloadEntry[];
      state.error = null;
      state.selectedChatType = currentChatType;
      state.selectedPolicyLocation =
        currentChatType === 'Policy'
          ? (sessionData as any).policy_location || defaultPolicyLocationValue
          : defaultPolicyLocationValue;

      // Restore session settings from backend, with localStorage fallback
      const persistedFallback = loadChatSettingsFromStorage();

      if (sessionData.system_instructions && sessionData.system_instructions.trim()) {
        // Use backend session data (highest priority)
        state.selectedSystemInstructions = sessionData.system_instructions;
        state.useCustomInstructions = true;
        // Update localStorage to match backend
        saveChatSettingsToStorage({
          selectedSystemInstructions: sessionData.system_instructions,
          useCustomInstructions: true,
        });
      } else if (persistedFallback.useCustomInstructions && persistedFallback.selectedSystemInstructions) {
        // Fallback to localStorage if backend has no custom instructions
        state.selectedSystemInstructions = persistedFallback.selectedSystemInstructions;
        state.useCustomInstructions = persistedFallback.useCustomInstructions;
      } else {
        // No custom instructions anywhere
        state.selectedSystemInstructions = '';
        state.useCustomInstructions = false;
      }

      // Restore temperature from session data, with localStorage fallback
      const backendTemperature = (sessionData as any).temperature;
      if (backendTemperature && [0.2, 1.0, 1.8].includes(backendTemperature)) {
        state.selectedTemperature = backendTemperature;
        saveChatSettingsToStorage({ selectedTemperature: backendTemperature });
      } else if (persistedFallback.selectedTemperature) {
        state.selectedTemperature = persistedFallback.selectedTemperature;
      } else {
        state.selectedTemperature = initialState.selectedTemperature;
      }

      state.isLoading = false;
      state.messages = messages.map(msg => ({ ...msg, feedback: null }));
    }),

    startNewChat: create.reducer<ChatType | undefined>((state, action) => {
      state.messages = [];
      state.isLoading = false;
      state.error = null;
      state.currentSessionId = null;
      state.firestoreSessionId = null;
      state.activePollingId = null;
      state.selectedChatType = (action.payload as ChatType) || initialState.selectedChatType;
      state.selectedTemperature = initialState.selectedTemperature;
      state.selectedSystemInstructions = initialState.selectedSystemInstructions;
      state.useCustomInstructions = initialState.useCustomInstructions;
      state.file_history = [];
      state.selectedPolicyLocation = defaultPolicyLocationValue;
    }),

    sendMessage: create.asyncThunk<ChatInitiateResponse, SendMessageThunkPayload>(
      async (payload, { getState }) => {
        const ChatState = (getState() as RootState).chat;
        const systemInstructionsToSend = ChatState.useCustomInstructions ? ChatState.selectedSystemInstructions : '';
        const initialResponse = await sendChatMessage(
          payload.prompt,
          ChatState.selectedChatType,
          ChatState.currentSessionId,
          ChatState.firestoreSessionId,
          ChatState.selectedTemperature,
          systemInstructionsToSend,
          payload.promptTemplateId,
          payload.promptTemplateIsGlobal,
          payload.files
        );
        return initialResponse;
      },
      {
        pending: (state, action) => {
          const { prompt, files } = action.meta.arg;

          if (files && files.length > 0) {
            const newFileEntries: BackendFilePayloadEntry[] = files.map(fileInfo => ({
              gcs_path: fileInfo.gcs_uri,
              mime_type: fileInfo.type,
              file_size: fileInfo.size,
              upload_time_utc: (fileInfo as any).upload_time_utc || new Date().toISOString(),
            }));

            const existingPaths = new Set(state.file_history.map(f => f.gcs_path));
            newFileEntries.forEach(newFile => {
              if (!existingPaths.has(newFile.gcs_path)) {
                state.file_history.push(newFile);
              }
            });
          }

          if (prompt || (files && files.length > 0)) {
            const userMessage: ChatMessage = {
              id: uuidv4(),
              role: 'user',
              text: prompt,
              timestamp: new Date().toISOString(),
              fileInfo: files,
            };
            state.messages.push(userMessage);
          }
          state.isLoading = true;
          state.error = null;
        },
        fulfilled: (state, action) => {
          if (action.payload.response_success && action.payload.firestore_session_id) {
            state.activePollingId = action.payload.firestore_session_id;
            if (action.payload.session_id) {
              state.currentSessionId = action.payload.session_id;
            }
            if (action.payload.firestore_session_id) {
              state.firestoreSessionId = action.payload.firestore_session_id;
            }
          } else {
            state.isLoading = false;
            state.error = action.payload.response || 'Failed to send message. Please check server logs.';
            const systemMessage: ChatMessage = {
              id: uuidv4(),
              role: 'system',
              text: `Error: ${state.error}`,
              timestamp: new Date().toISOString(),
            };
            state.messages.push(systemMessage);
          }
        },
        rejected: (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'An unknown error occurred during initiation';
          state.activePollingId = null;
        },
      }
    ),
    stopChatPolling: create.reducer(state => {
      state.isLoading = false;
      state.activePollingId = null;
    }),
  }),
  selectors: {
    selectMessages: state => state.messages,
    selectSelectedChatType: state => state.selectedChatType,
    selectSelectedTemperature: state => state.selectedTemperature,
    selectSelectedSystemInstructions: state => state.selectedSystemInstructions,
    selectUseCustomInstructions: state => state.useCustomInstructions,
    selectIsLoading: state => state.isLoading,
    selectError: state => state.error,
    selectCurrentSessionId: state => state.currentSessionId,
    selectFirestoreSessionId: state => state.firestoreSessionId,
    selectActivePollingId: state => state.activePollingId,
    selectFileHistory: state => state.file_history,
    selectPolicyLocationOptions: state => state.policyLocationOptions,
    selectSelectedPolicyLocation: state => state.selectedPolicyLocation,
  },
});

export const {
  setSelectedChatType,
  setSelectedTemperature,
  setSelectedSystemInstructions,
  setUseCustomInstructions,
  setSelectedPolicyLocation,
  addMessage,
  setChatIsLoading,
  setError,
  clearError,
  setSessionIds,
  setMessageFeedback,
  sendMessage,
  stopChatPolling,
  resumeChatSession,
  startNewChat,
} = chatSlice.actions;

export const {
  selectMessages,
  selectSelectedChatType,
  selectSelectedTemperature,
  selectSelectedSystemInstructions,
  selectUseCustomInstructions,
  selectIsLoading,
  selectError,
  selectCurrentSessionId,
  selectFirestoreSessionId,
  selectActivePollingId,
  selectFileHistory,
  selectPolicyLocationOptions,
  selectSelectedPolicyLocation,
} = chatSlice.selectors;

export default chatSlice.reducer;
