import json
import os
import typing

# from dotenv import load_dotenv, dotenv_values
# load_dotenv()

VALID_ENV_KEYS = typing.Literal["GLOBAL_SYSTEM_INSTRUCTIONS"]


def build_configuration_dict(dir_path, env_val):
    """Function to prepare the config data from base and env configs"""
    key = "config"
    config_files = (
        os.path.join(dir_path, "config", f"config.{env}.json")
        for env in ("base", env_val)
    )

    config_dict = {key: {}}
    print("CR env variables test: ", os.environ.get("REDIRECT_URL"))
    for file in config_files:
        with open(file) as f:
            config_dict[key].update(json.load(f)[key])

    client_id = os.environ.get("CLIENT_ID", None)
    client_secret = os.environ.get("CLIENT_SECRET", None)
    config_dict["config"]["credentials"]["client_id"] = client_id
    config_dict["config"]["credentials"]["client_secret"] = client_secret

    return config_dict


# directory reach

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


def get_config_val(parameter, ENV_VAL):
    """Function to get configuration values"""

    config_data = build_configuration_dict(BASE_DIR, ENV_VAL)

    config_data["config"].update({"env": ENV_VAL})

    value_mapping = {
        "credentials": "credentials",
        "emails_to_buckets": "emails_to_buckets",
        "domain_to_workbook_buckets": "domain_to_workbook_buckets",
        "file_upload": "file_upload",
        "service_account": "service_account",
        "pubsub": "pubsub",
        "policy_web": "policy_web",
        "domain_to_firestore_db": "domain_to_firestore_db",
        "global_firestore_db": "global_firestore_db",
        "retrieval_workbooks": "retrieval_workbooks",
        "project_number": "project_number",
    }

    return config_data["config"][value_mapping[parameter]]


def get_environment_variable(key: VALID_ENV_KEYS, default=None):
    valid_keys = typing.get_args(VALID_ENV_KEYS)
    if key in valid_keys:
        return os.getenv(key, default)
    else:
        return default
