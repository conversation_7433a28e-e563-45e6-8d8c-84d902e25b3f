import traceback
from datetime import datetime, timedelta, timezone
from typing import List

import google.cloud.exceptions as GCPExceptions
from flask import Blueprint, jsonify, make_response, request, session
from pydantic_core import ValidationError
from models.shared.enums import ModelName, SessionTypeEnum
from website.logger_framework import make_logger

from lib.firestore_client import Fire<PERSON>re<PERSON>lient
from services.PromptTemplateService import PromptTemplateService

from models.PromptTemplate import (
    PromptTemplate,
    CreatePromptTemplate,
    UpdatePromptTemplate
)
from .response_models import GetPromptTemplatesResponse

workflow = "prompt_templates_workflow"
logger_info, logger_error = make_logger(workflow, __file__)
prompt_templates_bp = Blueprint("prompt_templates_process", __name__)

@prompt_templates_bp.errorhandler(ValidationError)
def handle_pydantic_validation_error(error: ValidationError):
    logger_error.error(
        f"{workflow} : model validation : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.json(),
            "reason": "validation"
        }), 422
    )

@prompt_templates_bp.errorhandler(GCPExceptions.NotFound)
def handle_gcp_not_found(error: GCPExceptions.NotFound):
    logger_error.error(
        f"{workflow} : gcp not found: {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.message,
            "reason": "not found"
        }), 404
    )

@prompt_templates_bp.errorhandler(GCPExceptions.Conflict)
def handle_gcp_conflict(error: GCPExceptions.Conflict):
    logger_error.error(
        f"{workflow} : gcp conflict : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": error.message,
            "reason": "conflict"
        }), 422
    )

@prompt_templates_bp.errorhandler(Exception)
def handle_unexpected_error(error: Exception):
    logger_error.error(
        f"{workflow} : unexpected : {request.method} {request.path} : {str(error)} : {traceback.format_exc()}"
    )
    return make_response(
        jsonify({
            "error": str(error),
            "reason": "unexpected"
        }), 500
    )

@prompt_templates_bp.get("/my")
def get_authorized_prompt_templates():
    request_user = session.get("email")
    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    prompt_templates: List[PromptTemplate] = service.get_authorized_prompt_templates(request_user)
    response_body = GetPromptTemplatesResponse(
        user=request_user,
        promptTemplates=prompt_templates
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)

@prompt_templates_bp.get("/my/authored")
def get_authored_prompt_templates():
    request_user = session["email"]
    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    prompt_templates: List[PromptTemplate] = service.get_authored_prompt_templates(request_user, is_global=False)
    response_body = GetPromptTemplatesResponse(
        user=request_user,
        promptTemplates=prompt_templates
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)
    
@prompt_templates_bp.get("/my/type/<session_type>")
def get_prompt_templates_by_type(session_type: int):
    request_user = session["email"]
    session_type = SessionTypeEnum(session_type)

    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    prompt_templates: List[PromptTemplate] = service.get_prompt_templates_by_type(session_type, request_user, is_global=False)
    response_body = GetPromptTemplatesResponse(
        user=request_user,
        promptTemplates=prompt_templates
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)

@prompt_templates_bp.get("/my/<template_id>")
def get_prompt_template_by_id(template_id: str):
    request_user = session["email"]
    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    prompt_template: PromptTemplate | None = service.get_authorized_prompt_template_by_id(template_id, request_user, is_global=False)
    if not prompt_template:
        return make_response({
            "error": f"Prompt Template with id: {template_id} could not be found for user: {request_user}",
            "reason": "not found"
        }, 404)
    
    response_body = prompt_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
    return make_response(response_body, 200)

@prompt_templates_bp.get("/public")
def get_public_prompt_templates():
    client = FirestoreClient().GlobalInstanceClient()
    service = PromptTemplateService(persistence_client=client, global_persistence_client=client)

    prompt_templates: List[PromptTemplate] = service.get_global_prompt_templates()
    response_body = GetPromptTemplatesResponse(
        user=None,
        promptTemplates=prompt_templates
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)

@prompt_templates_bp.get("/public/<template_id>")
def get_global_prompt_template_by_id(template_id: str):
    request_user = session["email"]
    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    prompt_template: PromptTemplate | None = service.get_prompt_template_by_id(template_id, is_global=True)
    if not prompt_template:
        return make_response({
            "error": f"Public Prompt Template with id: {template_id} could not be found",
            "reason": "not found"
        }, 404)
    
    response_body = prompt_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
    return make_response(response_body, 200)

@prompt_templates_bp.get("/public/type/<session_type>")
def get_global_prompt_templates_by_type(session_type: int):
    session_type = SessionTypeEnum(session_type)

    client = FirestoreClient().GlobalInstanceClient()
    service = PromptTemplateService(persistence_client=client, global_persistence_client=client)

    prompt_templates: List[PromptTemplate] = service.get_prompt_templates_by_type(
        session_type=session_type,
        is_global=True
    )
    response_body = GetPromptTemplatesResponse(
        user=None,
        promptTemplates=prompt_templates
    )

    return make_response(response_body.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)

@prompt_templates_bp.post("/my")
def create_prompt_template():
    request_user: str = session.get("email")
    if not request_user:
        raise

    request_body: dict = request.get_json()
    template_name = request_body.get("name")
    prompt_text = request_body.get("prompt")
    session_type = request_body.get("sessionType", SessionTypeEnum.CHAT)
    if not isinstance(session_type, SessionTypeEnum):
        try:
            if isinstance(session_type, str) and session_type.isdigit():
                session_type = SessionTypeEnum(int(session_type))
            else:    
                session_type = SessionTypeEnum(session_type)
        except:
            session_type = SessionTypeEnum.CHAT
    
    temperature = float(request_body.get("temperature"))
    system_instructions = request_body.get("systemInstructions")
    model = ModelName.MEDLM if session_type == SessionTypeEnum.MDLM else ModelName.FLASH

    client = FirestoreClient().InstanceClient(user_email=request_user)
    service = PromptTemplateService(persistence_client=client)

    new_prompt_template: PromptTemplate = service.create_prompt_template(
        name=template_name,
        author_email=request_user,
        prompt_text=prompt_text,
        prompt_session_type=session_type,
        prompt_model=model,
        prompt_temperature=temperature,
        prompt_system_instructions=system_instructions
    )

    response_body = {
        "promptTemplate": new_prompt_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
    }
    return make_response(response_body, 200)

@prompt_templates_bp.patch("/my/<template_id>")
def update_prompt_templates(template_id: str):
    request_body: dict = request.get_json()
    request_user = session.get("email")
    session_type = request_body.get("sessionType", None)
    if session_type is not None and not isinstance(session_type, SessionTypeEnum):
        try:
            if isinstance(session_type, str) and session_type.isdigit():
                session_type = SessionTypeEnum(int(session_type))
            else:
                session_type = SessionTypeEnum(session_type)
        except Exception as e:
            raise e # maybe we should want to fail validation loudly here 

    updates = UpdatePromptTemplate(
        name=request_body.get("name", None),
        prompt=request_body.get("prompt", None),
        session_type=session_type,
        temperature=request_body.get("temperature", None),
        system_instructions=request_body.get("systemInstructions", None),
        added_tags=request_body.get("addedTags", None),
        removed_tags=request_body.get("removedTags", None),
        added_authorized_entities=request_body.get("addedAuthorizedEntities", None),
        removed_authorized_entities=request_body.get("removedAuthorizedEntities", None),
    )

    prompt_template_client = FirestoreClient().InstanceClient(user_email=request_user)
    prompt_template_service = PromptTemplateService(persistence_client=prompt_template_client)

    updated_template = prompt_template_service.update_prompt_template(
        prompt_template_id=template_id,
        prompt_template_updates=updates,
        user_email=request_user,
        is_global=False
    )
    
    return make_response(updated_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)
    
@prompt_templates_bp.delete("/my/<template_id>")
def delete_prompt_template_by_id(template_id: str):
    user = session.get('email')
    client = FirestoreClient().InstanceClient(user_email=user)
    prompt_template_service = PromptTemplateService(persistence_client=client)

    deleted: bool = prompt_template_service.delete_prompt_template(
        user_email=user,
        prompt_template_id=template_id,
        is_global=False
    )

    response_body = {
        "id": template_id,
        "deleted": deleted
    }
    return make_response(jsonify(response_body), 200 if deleted else 500)

@prompt_templates_bp.post("/public")
def create_global_prompt_template():
    request_user: str = session.get("email")
    if not request_user:
        return make_response({
            "error": "User not authenticated",
            "reason": "unauthorized"
        }, 401)

    request_body: dict = request.get_json()
    template_name = request_body.get("name")
    prompt_text = request_body.get("prompt")
    session_type = request_body.get("sessionType", SessionTypeEnum.CHAT)
    if not isinstance(session_type, SessionTypeEnum):
        try:
            if isinstance(session_type, str) and session_type.isdigit():
                session_type = SessionTypeEnum(int(session_type))
            else:    
                session_type = SessionTypeEnum(session_type)
        except:
            session_type = SessionTypeEnum.CHAT
    temperature = float(request_body.get("temperature", 1.0))
    system_instructions = request_body.get("systemInstructions")
    model = ModelName.MEDLM if session_type == SessionTypeEnum.MDLM else ModelName.FLASH

    # Use global client for public templates
    client = FirestoreClient().GlobalInstanceClient()
    service = PromptTemplateService(persistence_client=client, global_persistence_client=client)

    # Check if user has admin or core team permissions
    # TODO: validate user role has permissions wrt Global Prompt Templates

    new_prompt_template: PromptTemplate = service.create_global_prompt_template(
        name=template_name,
        author_email=request_user,
        prompt_text=prompt_text,
        prompt_session_type=session_type,
        prompt_model=model,
        prompt_temperature=temperature,
        prompt_system_instructions=system_instructions
    )

    response_body = {
        "promptTemplate": new_prompt_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True)
    }
    return make_response(response_body, 200)

@prompt_templates_bp.patch("/public/<template_id>")
def update_global_prompt_template(template_id: str):

    request_user: str = session.get("email")
    if not request_user:
        return make_response({
            "error": "User not authenticated",
            "reason": "unauthorized"
        }, 401)

    request_body: dict = request.get_json()
    session_type = request_body.get("sessionType", None)
    if session_type is not None and not isinstance(session_type, SessionTypeEnum):
        try:
            if isinstance(session_type, str) and session_type.isdigit():
                session_type = SessionTypeEnum(int(session_type))
            else:
                session_type = SessionTypeEnum(session_type)
        except Exception as e:
            return make_response({
                "error": f"Invalid session type: {session_type}",
                "reason": "validation"
            }, 422)

    updates = UpdatePromptTemplate(
        name=request_body.get("name", None),
        prompt=request_body.get("prompt", None),
        session_type=session_type,
        temperature=request_body.get("temperature", None),
        system_instructions=request_body.get("systemInstructions", None),
        added_tags=request_body.get("addedTags", None),
        removed_tags=request_body.get("removedTags", None),
        added_authorized_entities=request_body.get("addedAuthorizedEntities", None),
        removed_authorized_entities=request_body.get("removedAuthorizedEntities", None),
    )

    # Check if user has admin or core team permissions for global templates
    # TODO: validate user role has permissions wrt Global Prompt Templates
        # return make_response({
        #     "error": "User does not have permission to update global templates",
        #     "reason": "unauthorized"
        # }, 403)

    client = FirestoreClient().GlobalInstanceClient()
    prompt_template_service = PromptTemplateService(persistence_client=client, global_persistence_client=client)

    updated_template = prompt_template_service.update_prompt_template(
        prompt_template_id=template_id,
        prompt_template_updates=updates,
        user_email=request_user,
        is_global=True
    )
    
    return make_response(updated_template.to_dict(date_format_iso=True, include_document_id=True, to_camel=True), 200)

@prompt_templates_bp.delete("/public/<template_id>")
def delete_global_prompt_template_by_id(template_id: str):
    request_user: str = session.get("email")
    if not request_user:
        return make_response({
            "error": "User not authenticated",
            "reason": "unauthorized"
        }, 401)

    # Check if user has admin or core team permissions for global templates
    # TODO: validate user role has permissions wrt Global Prompt Templates
    
    client = FirestoreClient().GlobalInstanceClient()
    prompt_template_service = PromptTemplateService(persistence_client=client, global_persistence_client=client)

    deleted: bool = prompt_template_service.delete_prompt_template(
        user_email=request_user,
        prompt_template_id=template_id,
        is_global=True
    )

    response_body = {
        "id": template_id,
        "deleted": deleted
    }
    return make_response(jsonify(response_body), 200 if deleted else 500)
