import React, { useState, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';
import { MdOutlineSave, MdOutlineModeEdit } from 'react-icons/md';
import { GiCancel } from 'react-icons/gi';
import { Dialog } from '@base-ui-components/react/dialog';
import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';
import './ChatSettingsModal.scss';

interface ChatSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (temperature: number, systemInstructions?: string, useCustomInstructions?: boolean) => void;
  currentTemperature: number;
  currentSystemInstructions?: string;
  currentUseCustomInstructions?: boolean;
  isLoading?: boolean;
}

type TemperatureId = 'less-creative' | 'classic' | 'more-creative';

// Chat temperature configuration - aligned with chat system values
const chatTemperatureOptions: { id: TemperatureId; value: number; displayName: string }[] = [
  { id: 'less-creative', value: 0.2, displayName: 'Less Creative' },
  { id: 'classic', value: 1.0, displayName: 'Classic' },
  { id: 'more-creative', value: 1.8, displayName: 'More Creative' },
];

const defaultChatTemperatureOption = chatTemperatureOptions[1]; // 'classic'

// Temperature conversion functions for chat
export const chatTemperatureValueToOption = (value: number): TemperatureId => {
  const option = chatTemperatureOptions.find(opt => opt.value === value);
  return option ? option.id : defaultChatTemperatureOption.id;
};

export const chatTemperatureOptionToValue = (optionId: TemperatureId): number => {
  const option = chatTemperatureOptions.find(opt => opt.id === optionId);
  return option ? option.value : defaultChatTemperatureOption.value;
};

export const getChatTemperatureDisplayName = (value: number): string => {
  const option = chatTemperatureOptions.find(opt => opt.value === value);
  return option ? option.displayName : defaultChatTemperatureOption.displayName;
};

const ChatSettingsModal: React.FC<ChatSettingsModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentTemperature,
  currentSystemInstructions = '',
  currentUseCustomInstructions = false,
  isLoading = false,
}) => {
  const [selectedTemperature, setSelectedTemperature] = useState<TemperatureId>('classic');
  const [systemInstructions, setSystemInstructions] = useState<string>('');
  const [useCustomInstructions, setUseCustomInstructions] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form with current values when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedTemperature(chatTemperatureValueToOption(currentTemperature));
      setSystemInstructions(currentSystemInstructions);
      setUseCustomInstructions(currentUseCustomInstructions);
    }
  }, [isOpen, currentTemperature, currentSystemInstructions, currentUseCustomInstructions]);

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);
    try {
      const temperatureValue = chatTemperatureOptionToValue(selectedTemperature);
      const instructionsToSave = useCustomInstructions ? systemInstructions : '';
      onSave(temperatureValue, instructionsToSave, useCustomInstructions);
      onClose();
    } catch (error) {
      console.error('Failed to save chat settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to current values
    setSelectedTemperature(chatTemperatureValueToOption(currentTemperature));
    setSystemInstructions(currentSystemInstructions);
    setUseCustomInstructions(currentUseCustomInstructions);
    onClose();
  };

  const isButtonDisabled = isSaving || isLoading;

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Backdrop className="chat-settings-modal-overlay" />
        <Dialog.Popup className="chat-settings-modal">
          {/* Close Button */}
          <Dialog.Close className="chat-settings-modal__close-btn" aria-label="Close modal">
            <IoClose className="w-6 h-6" />
          </Dialog.Close>

          {/* Content */}
          <div className="chat-settings-modal__content">
            {/* Title */}
            <Dialog.Title className="chat-settings-modal__title">Chat Settings</Dialog.Title>

            {/* Temperature Section */}
            <section className="chat-settings-modal__temperature-section">
              <h3 className="chat-settings-modal__temperature-title">Temperature</h3>
              <p className="chat-settings-modal__temperature-description">
                Temperature is a parameter that allows you to control the creativity of Sidekick's generated output
              </p>

              {/* Radio Button Group */}
              <div className="chat-settings-modal__radio-group">
                <RadioGroup
                  value={selectedTemperature}
                  onValueChange={value => setSelectedTemperature(value as TemperatureId)}
                  className="flex items-center gap-6"
                >
                  {chatTemperatureOptions.map(option => (
                    <label key={option.id} className="chat-settings-modal__radio-option">
                      <Radio.Root value={option.id} className="chat-settings-modal__radio-input">
                        <Radio.Indicator className="chat-settings-modal__radio-indicator" />
                      </Radio.Root>
                      <span>{option.displayName}</span>
                    </label>
                  ))}
                </RadioGroup>
              </div>
            </section>

            {/* System Instructions Section */}
            <section className="chat-settings-modal__system-instructions">
              <h3 className="chat-settings-modal__system-instructions-title">System Instructions (optional)</h3>
              <p className="chat-settings-modal__system-instructions-description">
                System instructions in Gemini models guide the model's behavior by providing additional context and
                constraints beyond the user's prompt. They can be used to define a persona, output format, style, tone,
                and goals, influencing the model's responses.
              </p>

              {/* Instruction Type Selection */}
              <div className="chat-settings-modal__instruction-type-section">
                <div className="chat-settings-modal__instruction-type-options">
                  <RadioGroup
                    value={useCustomInstructions ? 'custom' : 'default'}
                    onValueChange={value => setUseCustomInstructions(value === 'custom')}
                    className="flex items-center gap-6"
                  >
                    <label className="chat-settings-modal__instruction-type-option">
                      <Radio.Root value="default" className="chat-settings-modal__radio-input">
                        <Radio.Indicator className="chat-settings-modal__radio-indicator" />
                      </Radio.Root>
                      <span>Default Instructions</span>
                    </label>
                    <label className="chat-settings-modal__instruction-type-option">
                      <Radio.Root value="custom" className="chat-settings-modal__radio-input">
                        <Radio.Indicator className="chat-settings-modal__radio-indicator" />
                      </Radio.Root>
                      <span>Custom Instructions</span>
                    </label>
                  </RadioGroup>
                </div>
              </div>

              {/* Textarea Container - only show when custom is selected */}
              {useCustomInstructions && (
                <div className="chat-settings-modal__textarea-container">
                  <textarea
                    className="chat-settings-modal__textarea"
                    value={systemInstructions}
                    onChange={e => setSystemInstructions(e.target.value)}
                    placeholder="Enter your system instructions here..."
                    disabled={isButtonDisabled}
                  />
                  <MdOutlineModeEdit className="chat-settings-modal__textarea-icon" />
                </div>
              )}
            </section>
          </div>

          {/* Footer */}
          <div className="chat-settings-modal__footer">
            <button
              onClick={handleCancel}
              className="chat-settings-modal__button chat-settings-modal__button--cancel"
              disabled={isButtonDisabled}
            >
              <GiCancel size={20} />
              <span>Cancel</span>
            </button>
            <button
              onClick={handleSave}
              disabled={isButtonDisabled}
              className="chat-settings-modal__button chat-settings-modal__button--save"
            >
              <MdOutlineSave className="w-5 h-5" />
              <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ChatSettingsModal;
