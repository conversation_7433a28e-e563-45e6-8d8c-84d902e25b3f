import traceback
from datetime import datetime
from typing import Dict, List

from google.api_core.exceptions import InvalidArgument
from vertexai.generative_models import (
    ChatSession,
    Content,
    GenerationConfig,
    GenerationResponse,
    GenerativeModel,
    HarmBlockThreshold,
    HarmCategory,
    Part,
    ResponseValidationError,
    SafetySetting,
)

# models.shared
from models.shared.enums import ModelName, SessionTypeEnum
from website.config import get_environment_variable
from website.google_cloud_init import GCS
from website.logger_framework import make_logger
from website.utils import log_helper

workflow = "chat_workflow"

logger_info, logger_error = make_logger(workflow, __file__)


class CreateGenerativeModel:
    """Wrapper for the GenerativeModel class"""

    safety_settings = {
        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_ONLY_HIGH,
        HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
        HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
        HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
    }

    MAX_TOKEN: int = 1000000

    MAX_TEMPERATURE: int | float = 2.0
    DEFAULT_TEMPERATURE: int | float = 1.0
    MIN_TEMPERATURE: int | float = 0.0

    ERR_MSG = ""

    def __init__(
        self,
        model_name: str,
        system_instructions: str | None = None,
        use_global_instructions: bool = True,
    ):
        """Initialize CreateGenerativeModel with system instructions configuration.

        Args:
            model_name (str): Model being used
            system_instructions (str, optional): Additional system instructions to append. 
                                                Defaults to None (uses only global instructions).
            use_global_instructions (bool, optional): Whether to include global system instructions 
                                                     from environment variables. Defaults to True.
                                                     When True: Uses global + additional instructions
                                                     When False: Uses only the provided instructions

        Default Behavior:
            - When both parameters use defaults: Uses global system instructions from environment
            - When system_instructions provided + use_global_instructions=True: Uses global + additional
            - When use_global_instructions=False: Uses only the provided system_instructions

        Sets:
            self.model: (GenerativeModel): Googles AI class with configured system instructions
        """

        if isinstance(model_name, str):
            self.model_name = getattr(
                ModelName, model_name, ModelName.FLASH.value)
        else:
            self.model_name = ModelName.FLASH.value

        # Build system instructions based on configuration
        system_instructions_parts = []

        if use_global_instructions:
            global_system_instruction = get_environment_variable(
                "GLOBAL_SYSTEM_INSTRUCTIONS", ""
            )
            logger_info.info(
                f"ENV GLOBAL_SYSTEM_INSTRUCTIONS: {global_system_instruction}")
            if global_system_instruction:
                system_instructions_parts.append(global_system_instruction)
        else:
            logger_info.info(
                "Skipping global system instructions (use_global_instructions=False)")

        if system_instructions:
            system_instructions_parts.append(system_instructions)

        self.system_instructions = "\n\n".join(system_instructions_parts)

        self.model = GenerativeModel(
            model_name=self.model_name,
            system_instruction=self.system_instructions,
            safety_settings=self.safety_settings,
        )

    @staticmethod
    def _message_parts(
        email: str,
        prompt: str,
        file_history: List[dict] = [],
    ) -> List[Part]:
        """Append file_history & latest prompt to current session.

        Args:
            email (str): Required for GCS().verify_file
            prompt (str): The latest prompt
            file_history (List[dict], optional): current file history. Defaults to [].

        Returns:
            List[Part]: Message to be sent to VertexAI
        """
        if file_history is not None:
            parts_list: List[Part] = []
            for file in file_history:
                gcs_uri = file["gcs_path"]
                mime_type = file["mime_type"]
                if GCS().verify_file(gcs_uri, email):
                    parts_list.append(Part.from_uri(gcs_uri, mime_type))
        parts_list.append(Part.from_text(prompt))
        return parts_list

    def _count_tokens(self, user_message: List[Part], user_dict: dict) -> int:
        """Counts current tokens

        Args:
            user_message (List[Part]): Message to be sent to VertexAI
            user_dict (dict): for logging purposes

        Raises:
            InvalidArgument: failure in counting tokens, normally caused by unknown issue with .txt files.
            InvalidArgument: Token count exceeds MAX_TOKEN

        Returns:
            int: _description_
        """
        self.tokens = 0
        self.user_dict = user_dict
        try:
            self.tokens = self.model.count_tokens(user_message).total_tokens
            consumed_tokens = (self.tokens / self.MAX_TOKEN) * 100
            self.user_dict["tokens"] = self.tokens
        except InvalidArgument as err:
            logger_error.error(
                "%s : _count_tokens : Failure in count tokens: %s", workflow, str(err)
            )
            # tokens might = -1 due to the error.
            self.ERR_MSG = log_helper.process_invalid_arg(str(err), self.user_dict)
            self.user_dict["error"] = str(err)
            raise InvalidArgument(
                f"Failure in count tokens. Session info: {self.user_dict}"
            ) from err  # Outputs 2 errors, ours + the actual error.
        if self.tokens > self.MAX_TOKEN:

            self.ERR_MSG = f"Session too large. Please reduce the size of the entered text or uploaded file. Total session length: {consumed_tokens:.2f}%"
            raise InvalidArgument(
                f"Unable to submit prompt due to data in prompt contents exceeding model expectations. Session info: {self.user_dict}"
            )

        return self.tokens

    def re_init_session(
        self,
        conversation_history: List[Content] = [],
    ) -> ChatSession:
        """Re-inits during a session based off Firestore fields.

        Args:
            conversation_history (List[Content], optional): current chat history. Defaults to [].

        Sets:
            self.session (ChatSession): instance populated with current file_history & conversation_history.
        Returns:
            ChatSession: instance populated with current file_history & conversation_history.
        """

        if conversation_history:
            session = self.model.start_chat(
                history=self.listmessage_to_content(conversation_history)
            )
        else:
            session = self.model.start_chat()

        return session

    def send_prompt(
        self,
        prompt: str,
        email: str,
        user_dict: dict,
        temperature: int | float = DEFAULT_TEMPERATURE,
        file_history: List[dict] = [],
        conversation_history: List[Content] = [],
    ) -> GenerationResponse:
        """Sends a chat message to provided model

        Args:
            prompt (str): The latest prompt
            email (str): for verifying if file exists
            user_dict (dict): for logging purposes
            temperature (int | float, optional): Model temperature. Defaults to DEFAULT_TEMPERATURE.
            file_history (List[dict], optional): current file history. Defaults to [].
            conversation_history (List[Content], optional): current chat history. Defaults to [].

        Raises:
            ValueError: prompt is `None` or a null string

        Returns:
            GenerationResponse: _description_
        """

        if prompt is None or prompt == "":
            raise ValueError("prompt is None or a null string")
        if (
            temperature is not None
        ):  # If setting temperature as None in the args, this fails.
            self.temperature = max(
                self.MIN_TEMPERATURE, min(temperature, self.MAX_TEMPERATURE)
            )  # clamp to [0, 2] inclusive
        else:
            self.temperature = self.DEFAULT_TEMPERATURE
        self.config = GenerationConfig(temperature=self.temperature)

        session = self.re_init_session(conversation_history)

        user_message = self._message_parts(email, prompt, file_history)

        self.user_dict = user_dict
        self._count_tokens(user_message, self.user_dict)
        try:
            response: GenerationResponse = session.send_message(
                user_message, generation_config=self.config
            )
        except ResponseValidationError as err:
            # https://cloud.google.com/vertex-ai/generative-ai/docs/reference/python/latest/vertexai.preview.generative_models.FinishReason
            logger_error.error(
                "%s : chat_response : Failure in response validation: %s",
                workflow,
                str(err),
            )
            self.user_dict["error"] = str(err)
            err_msg = f"The model response did not complete successfully."
            reason = 0
            if "finish reason: 2" in str(err).lower():
                err_msg = f"{err_msg} Finish Reason 2: Model output limit reached"
                reason = 2
            elif "finish reason: 3" in str(err).lower():
                err_msg = f"{err_msg} Finish Reason 3: Model content potentially contains safety violations"
                reason = 3
            elif "finish reason: 4" in str(err).lower():
                err_msg = f"{err_msg} Finish Reason 4: Model content potentially contains copyright violations"
                reason = 4
            raise Exception(
                f"The model response did not complete successfully. Finish Reason {reason}; Session info: {self.user_dict}"
            ) from err
        logger_info.info(
            f"{workflow} : chat_response : Returning API call. Session info: {self.user_dict}"
        )
        return response

    def chat_response(
        self,
        prompt: str,
        email: str,
        user_dict: dict,
        temperature: int | float = DEFAULT_TEMPERATURE,
        file_history: List[dict] = [],
        conversation_history: List[Part] = [],
    ):
        """Reinits the model & sends the latest prompt to the chat session & returns the response

        Args:
            model_name (str): model being used
            prompt (str): Current user prompt
            email (str): User email
            user_dict (dict): For logging purposes
            system_instructions (str, optional): The system instruction. Defaults to global system instruction.
            temperature (int | float, optional): Model temperature. Defaults to DEFAULT_TEMPERATURE.
            file_history (List[dict], optional): current file history. Defaults to [].
            conversation_history (List[Part], optional): current chat history. Defaults to [].

        Returns:
            tuple: A tuple containing a boolean indicating success, the response text, and the error message.
        """
        try:
            self.ERR_MSG = ""
            response = self.send_prompt(
                prompt,
                email,
                user_dict,
                temperature,
                file_history,
                conversation_history,
            )

            return True, response.text, ""
        except Exception as err:
            logger_error.error(
                "%s : chat_response : %s : %s",
                workflow,
                str(err),
                traceback.format_exc(),
            )
            logger_info.info(
                f"{workflow} : chat_response : Returning API error. Session info: {user_dict}"
            )
            if self.ERR_MSG == "":
                self.ERR_MSG = str(err)
            return (
                False,
                f"""Something went wrong. Please refresh and try again! Error: {self.ERR_MSG}""",  #
                str(err),  # This is only for our logs to tie the user to the error.
            )

    @staticmethod
    def session_type_to_model_name(session_type: str) -> str:
        """model_type dict is to be replaced with enums"""
        if isinstance(session_type, SessionTypeEnum):
            session_type = session_type
        elif isinstance(session_type, int) and session_type in (
            member.value for member in SessionTypeEnum
        ):
            session_type = SessionTypeEnum(session_type)
        elif isinstance(session_type, str) and session_type.upper() in [
            ste.name for ste in SessionTypeEnum
        ]:
            session_type = SessionTypeEnum[session_type.upper()]
        else:
            session_type = SessionTypeEnum.CHAT
        if session_type == SessionTypeEnum.CHAT:
            model = ModelName.FLASH.value
        elif session_type == SessionTypeEnum.CODE:
            model = ModelName.FLASH.value
        else:
            model = ModelName.MEDLM.value

        return model

    @staticmethod
    def listmessage_to_content(message_history: list):
        """Wrapper for vertexai.generative_models.Content.from_dict() function"""

        try:
            if isinstance(message_history, list):
                return_history = []
                for message in message_history:
                    return_history.append(Content.from_dict(message))
                return return_history

            return []

        except Exception as e:
            logger_error.error(
                f"{workflow} : listmessage_to_content : {str(e)} : {traceback.format_exc()}"
            )

    @staticmethod
    def generate_dynamic_system_instruction_part(email: str) -> str:
        """function to generate dynamic system instruction"""
        dynamic_system_instruction = ""
        if not email:
            return dynamic_system_instruction

        user_name_info = email.split("@")[0]
        user_name_parts = user_name_info.split(".")
        capitalized_user_name_parts = [part.capitalize() for part in user_name_parts]
        user_display_name = " ".join(capitalized_user_name_parts)

        current_system_time = datetime.now()
        current_display_datetime = current_system_time.strftime("%Y-%m-%d %H-%M-%S")

        dynamic_system_instruction = f"""\n\nYou are speaking to {user_display_name} \n
                    The current time is {current_display_datetime}.\n"""
        return dynamic_system_instruction

# if __name__ == "__main__":
# CHAT = CreateGenerativeModel(model_type["chat"]["type"]).model
# session = CHAT.start_chat()
# response = session.send_message(
#     "Write a hello world prompt",
#     generation_config=GenerationConfig(temperature=0.1),
# )
# session = CreateGenerativeModel.re_init_session(model_type["chat"]["type"])
# response = session.send_message(
#     "Write a hello world prompt",
#     generation_config=GenerationConfig(temperature=0.1),
# )

# response = CreateGenerativeModel(
#     model_type["chat"]["type"],
#     "Be as verbose as possible in your answer, add unnecessary comments & functions. Make the code as obtuse as possible",
# ).send_message(
#     "Write a hello world prompt", "<EMAIL>", {"mime_and_size": ""}
# )

# response = CreateGenerativeModel.chat_response(
#     "chat",
#     "Write a hello world prompt",
#     "<EMAIL>",
#     {"mime_and_size": ""},
#     "Be as verbose as possible in your answer, add unnecessary comments & functions. Make the code as obtuse as possible",
# )

# print(response)

# print(CreateGenerativeModel.session_type_to_model_name("chat"))
