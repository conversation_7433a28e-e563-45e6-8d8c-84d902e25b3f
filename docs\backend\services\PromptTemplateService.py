import sys
from datetime import datetime, timezone
from typing import List, Optional, MutableSequence, MutableMapping

from firebase_admin.firestore import firestore
import google.cloud.exceptions as GCPExceptions

from models.PromptTemplate import (
    PromptTemplate,
    CreatePromptTemplate,
    UpdatePromptTemplate
)

from models.shared.enums import (
    ModelName,
    ModelTemperatures, 
    SessionTypeEnum
)

from lib.firestore_client import (
    FirestoreLimits, 
    FirestoreLimitException, 
    FirestoreClient
)


class PromptTemplateService:
    def __init__(self, persistence_client: firestore.Client, global_persistence_client: firestore.Client = None):
        self._persistence_client: firestore.Client = persistence_client
        self._global_persistence_client: firestore.Client = global_persistence_client if global_persistence_client else FirestoreClient.GlobalInstanceClient()

    def _get_prompt_template_dict_with_deduplicated_fields(self, prompt_template_dict: dict) -> dict:
        """
        Removes duplicate fields from a prompt template dictionary.
        Currently removes the redundant 'id' field if it exists in the dictionary.
        
        Parameters
        ----------
        prompt_template_dict : dict
            Dictionary representation of a prompt template

        Returns
        -------
        dict
            Cleaned dictionary with deduplicated fields
        
        Method & its usage can be deprecated Once firestore documents are updated without 'id' field
        """
        if prompt_template_dict.get('id'):
            del prompt_template_dict['id']

        return prompt_template_dict

    def _get_prompt_template_by_id(
        self,
        id: str,
        user_email: str | None = None,
        authorize_user: bool = False,
        is_global: bool = False,
    ) -> PromptTemplate | None:
        client = (
            self._global_persistence_client if is_global else self._persistence_client
        )
        prompt_template_ref = client.collection(PromptTemplate.COLLECTION_NAME)\
            .document(id)

        prompt_template_doc = prompt_template_ref.get()
        if not prompt_template_doc.exists:
            return None
        
        as_dict = prompt_template_doc.to_dict()
        as_dict = self._get_prompt_template_dict_with_deduplicated_fields(as_dict)
        prompt_template = PromptTemplate(id=prompt_template_doc.id, **as_dict)
        
        if user_email and authorize_user:
            user_is_author = user_email == prompt_template.author
            user_has_access = user_email in prompt_template.authorized_entities
            if user_is_author or user_has_access:
                return prompt_template
            return None # Throw Unauthorized exception?   
        else:
            return prompt_template
    
    def _get_prompt_templates_from_query(
        self,
        query_ref: firestore.Query
    ) -> List[PromptTemplate]:
        prompt_template_docs = query_ref.stream()

        prompt_templates = []
        for doc in prompt_template_docs:
            if doc.exists:
                doc_dict = doc.to_dict()
                doc_dict = self._get_prompt_template_dict_with_deduplicated_fields(doc_dict)
                prompt_templates.append(PromptTemplate(id=doc.id, **doc_dict))

        #TODO: After entities in Firestore are updated without additional `id` field, below should be used.
        # prompt_templates = [
        #     PromptTemplate(id=doc.id, **(doc.to_dict()))
        #     for doc in prompt_template_docs
        #     if doc.exists
        # ]
        return prompt_templates

    def create_prompt_template(
        self,
        name: str,
        author_email: str,
        prompt_text: str,
        document_id: Optional[str] = None,
        prompt_temperature: Optional[float] = None,
        prompt_system_instructions: Optional[str] = None,
        prompt_model: ModelName = ModelName.FLASH,
        prompt_session_type: SessionTypeEnum = SessionTypeEnum.CHAT,
        tags: List[str] = [],
        authorized_entities: List[str] = []
    ) -> PromptTemplate:
        """
        `create_prompt_template` creates a `PromptTemplate` and attempts to persist it

        Parameters
        ----------
        document_id : str, optional
            Overrides auto-generated document `id` with `document_id`

        Returns
        -------
        `PromptTemplate`

        Raises
        ------
        `FirestoreLimitException`
            Local exception class that reports the parameter that was exceeded and its value
        `google.cloud.exceptions.Conflict`
            If a `PromptTemplate` with the same `document_id` already exists
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
        """
        author_email = author_email.strip().lower()
        prompt_template = CreatePromptTemplate(
            name=name,
            author=author_email,
            created_by=author_email,
            prompt=prompt_text,
            temperature=prompt_temperature or ModelTemperatures.DEFAULT_TEMPERATURE,
            model=prompt_model,
            session_type=prompt_session_type,
            system_instructions=prompt_system_instructions,
            tags=tags,
            authorized_entities={author_email, *authorized_entities}
        )

        document_string_size_bytes = sys.getsizeof(prompt_template.to_dict(date_format_iso=True))
        if document_string_size_bytes >= FirestoreLimits.DOCUMENT_SIZE_BYTES:
            raise FirestoreLimitException.create_document_size_limit_exception(document_string_size_bytes)
        
        collection_ref = self._persistence_client\
            .collection(PromptTemplate.COLLECTION_NAME)
        as_dict = prompt_template.to_dict(to_exclude={'files': True})

        created_at, prompt_template_ref = collection_ref.add(
            document_data=as_dict, 
            document_id=document_id
        )

        new_prompt_template = PromptTemplate(
            id=prompt_template_ref.id,
            **prompt_template.to_dict(date_format_iso=False)
        )

        return new_prompt_template
    
    def get_prompt_template_by_id(
        self,
        id: str,
        is_global: bool = False
    ) -> PromptTemplate | None:
        return self._get_prompt_template_by_id(
            id=id,
            authorize_user=False,
            is_global=is_global
        )
    
    def get_authorized_prompt_template_by_id(
        self,
        id: str,
        user_email: str,
        is_global: bool = False
    ) -> PromptTemplate | None:
        return self._get_prompt_template_by_id(
            id=id,
            user_email=user_email,
            authorize_user=True,
            is_global=is_global
        )
    
    def get_authored_prompt_templates(
        self,
        user_email: str,
        is_global: bool = False
    ) -> List[PromptTemplate]:
        client = self._global_persistence_client if is_global else self._persistence_client
        query_ref = client.collection(PromptTemplate.COLLECTION_NAME)\
            .where(filter=firestore.Or(
                [
                    firestore.FieldFilter("author", "==", user_email.strip().lower()),
                    firestore.FieldFilter("created_by", "==", user_email.strip().lower())
                ]
            ))

        prompt_templates = self._get_prompt_templates_from_query(query_ref)
        return prompt_templates
    
    def get_authorized_prompt_templates(
        self,
        user_email: str,
    ) -> List[PromptTemplate]:
        client = self._persistence_client
        query_ref = client.collection(PromptTemplate.COLLECTION_NAME)\
            .where("authorized_entities", "array_contains", user_email.strip().lower())
        
        prompt_templates = self._get_prompt_templates_from_query(query_ref)
        return prompt_templates
    
    def get_prompt_templates_by_type(
        self,
        session_type: SessionTypeEnum,
        user_email: Optional[str] = None,
        is_global: Optional[bool] = False
    ) -> List[PromptTemplate]:
        client = self._global_persistence_client if is_global else self._persistence_client
        query_ref = client.collection(PromptTemplate.COLLECTION_NAME)\
            .where("session_type", "in", [session_type.name, session_type.name.lower(), session_type.value])
        if user_email: # authorized_entities
            query_ref = query_ref.where("authorized_entities", "array_contains", user_email.strip().lower())
        
        prompt_templates = self._get_prompt_templates_from_query(query_ref)
        return prompt_templates
    
    def get_global_prompt_templates(
        self
    ) -> List[PromptTemplate]:
        client = self._global_persistence_client

        query_ref = client.collection(PromptTemplate.COLLECTION_NAME)

        prompt_templates = self._get_prompt_templates_from_query(query_ref)
        return prompt_templates

    def update_prompt_template(
        self,
        prompt_template_id: str,
        prompt_template_updates: UpdatePromptTemplate,
        is_global: bool = False,
        user_email: Optional[str] = None
    ) -> PromptTemplate | None:
        client = (
            self._global_persistence_client if is_global else self._persistence_client
        )

        field_updates = {}
        for k, v in prompt_template_updates.model_dump().items():
            if v is not None:
                # Explicityly converting Enum instances to their values as encountering error
                if isinstance(v, SessionTypeEnum):
                    field_updates[k] = v.value
                elif not isinstance(v, (MutableSequence, MutableMapping)):
                    field_updates[k] = v

        if prompt_template_updates.added_tags:
            field_updates["tags"] = firestore.ArrayUnion(
                prompt_template_updates.added_tags
            )
        if prompt_template_updates.removed_tags:
            field_updates["tags"] = firestore.ArrayRemove(
                prompt_template_updates.removed_tags
            )
        if prompt_template_updates.added_authorized_entities:
            field_updates["authorized_entities"] = firestore.ArrayUnion(
                prompt_template_updates.added_authorized_entities
            )
        if prompt_template_updates.removed_authorized_entities:
            field_updates["authorized_entities"] = firestore.ArrayRemove(
                prompt_template_updates.removed_authorized_entities
            )
        field_updates['updated_utc'] = datetime.now(timezone.utc)

        prompt_template_ref: firestore.DocumentReference = client\
            .collection(PromptTemplate.COLLECTION_NAME)\
            .document(prompt_template_id)
        pt_snapshot = prompt_template_ref.get()

        # Delete the block after entities in Firestore are updated without additional `id` field
        pt_snapshot_dict = pt_snapshot.to_dict()
        pt_snapshot_dict = self._get_prompt_template_dict_with_deduplicated_fields(pt_snapshot_dict)
        pt_snapshot = PromptTemplate(id=pt_snapshot.id, **pt_snapshot_dict)

        #TODO: After entities in Firestore are updated without additional `id` field, below should be used.
        # pt_snapshot = PromptTemplate(id=pt_snapshot.id, **pt_snapshot.to_dict())
        if user_email and user_email != pt_snapshot.author:
            raise GCPExceptions.Unauthorized(message=f"{user_email} is not authorized to modify PromptTemplate with id: {prompt_template_id}")
        
        update_result: firestore.types.WriteResult = prompt_template_ref.update(field_updates)
        if not update_result:
            None
        return self.get_prompt_template_by_id(prompt_template_id, is_global=is_global)

    def delete_prompt_template(
        self,
        prompt_template_id: str,
        is_global: bool = False,
        user_email: Optional[str] = None
    ) -> bool:
        client = (
            self._global_persistence_client if is_global else self._persistence_client
        )

        prompt_template_ref: firestore.DocumentReference = client\
            .collection(PromptTemplate.COLLECTION_NAME)\
            .document(prompt_template_id)
        pt_snapshot = prompt_template_ref.get()

        # Delete the block after entities in Firestore are updated without additional `id` field
        pt_snapshot_dict = pt_snapshot.to_dict()
        pt_snapshot_dict = self._get_prompt_template_dict_with_deduplicated_fields(pt_snapshot_dict)
        pt_snapshot = PromptTemplate(id=pt_snapshot.id, **pt_snapshot_dict)

        #TODO: After entities in Firestore are updated without additional `id` field, below should be used.
        # pt_snapshot = PromptTemplate(id=pt_snapshot.id, **pt_snapshot.to_dict())
        if user_email and user_email != pt_snapshot.author:
            raise GCPExceptions.Unauthorized(message=f"{user_email} is not authorized to modify PromptTemplate with id: {prompt_template_id}")
        
        prompt_template_ref.delete()
        return True

    def create_global_prompt_template(
        self,
        name: str,
        author_email: str,
        prompt_text: str,
        document_id: Optional[str] = None,
        prompt_temperature: Optional[float] = None,
        prompt_system_instructions: Optional[str] = None,
        prompt_model: ModelName = ModelName.FLASH,
        prompt_session_type: SessionTypeEnum = SessionTypeEnum.CHAT,
        tags: List[str] = []
    ) -> PromptTemplate:
        """
        `create_global_prompt_template` creates a `PromptTemplate` and attempts to persist it

        Parameters
        ----------
        document_id : str, optional
            Overrides auto-generated document `id` with `document_id`

        Returns
        -------
        `PromptTemplate`

        Raises
        ------
        `FirestoreLimitException`
            Local exception class that reports the parameter that was exceeded and its value
        `google.cloud.exceptions.Conflict`
            If a `PromptTemplate` with the same `document_id` already exists
        `google.api_core.exceptions.InvalidArgument`
            Catch-all error to expect for unhandled/irrecoverable Firestore issues
        `pydantic_core.ValidationError`
        """
        author_email = author_email.strip().lower()
        prompt_template = CreatePromptTemplate(
            name=name,
            author=author_email,
            created_by=author_email,
            prompt=prompt_text,
            temperature=prompt_temperature,
            model=prompt_model,
            session_type=prompt_session_type,
            system_instructions=prompt_system_instructions,
            tags=tags
        )

        document_string_size_bytes = sys.getsizeof(prompt_template.to_dict(date_format_iso=True))
        if document_string_size_bytes >= FirestoreLimits.DOCUMENT_SIZE_BYTES:
            raise FirestoreLimitException.create_document_size_limit_exception(document_string_size_bytes)
        
        collection_ref = self._global_persistence_client\
            .collection(PromptTemplate.COLLECTION_NAME)
        as_dict = prompt_template.to_dict(to_exclude={'files': True})

        created_at, prompt_template_ref = collection_ref.add(
            document_data=as_dict, 
            document_id=document_id
        )

        new_prompt_template = PromptTemplate(
            id=prompt_template_ref.id,
            **prompt_template.to_dict(date_format_iso=False)
        )

        return new_prompt_template
