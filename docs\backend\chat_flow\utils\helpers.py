import json
import sys
import threading
import traceback
from datetime import datetime, timezone
from time import time
from typing import List, Optional
from uuid import uuid4, uuid5

from flask import Request
from google.api_core.exceptions import InvalidArgument

from lib.firestore_client import FirestoreClient, FirestoreLimitException
from lib.gemini_helpers import session_to_file_history, session_to_gemini_history
from models.PromptTemplate import PromptTemplate
from models.Session import (
    CreateSession,
    CreateSessionMessage,
    Session,
    SessionFile,
    SessionMessage,
)
from models.shared.enums import ModelName, ModelTemperatures, SessionTypeEnum
from services.SessionService import SessionService
from services.PromptTemplateService import PromptTemplateService

from website.chat_workflow.utils.CreateGenerativeModel import (
    CreateGenerativeModel,
    Part,
)
from website.logger_framework import make_logger
from website.utils import db_utils, log_helper, timings

TZ = timezone.utc

workflow = "chat_workflow"

logger_info, logger_error = make_logger(workflow, __file__)


def _create_model_with_system_instructions(
    model_name: str,
    email: str,
    custom_system_instructions: str = ""
) -> CreateGenerativeModel:
    """
    Creates a GenerativeModel with appropriate system instructions based on user preference.

    Args:
        model_name: The name of the model to create
        email: User email for dynamic instructions
        custom_system_instructions: Custom instructions from user (empty = use default/global)

    Returns:
        Configured GenerativeModel instance

    Design:
        - If custom_system_instructions provided: Use ONLY custom + dynamic (no global)
        - If no custom instructions: Use global + dynamic (standard behavior)
    """
    dynamic_system_instruction = CreateGenerativeModel.generate_dynamic_system_instruction_part(
        email)

    if custom_system_instructions and custom_system_instructions.strip():
        # CUSTOM MODE: Replace global with custom instructions
        logger_info.info(
            f"{workflow} : Using custom system instructions for user {email}")

        # Combine dynamic + custom instructions, explicitly disable global
        final_system_instructions = f"{dynamic_system_instruction}\n\n{custom_system_instructions}".strip(
        )

        return CreateGenerativeModel(
            model_name=model_name,
            system_instructions=final_system_instructions,
            use_global_instructions=False
        )
    else:
        # logger_info.info(
        #     f"{workflow} : Using default (global) system instructions for user {email}")

        return CreateGenerativeModel(
            model_name=model_name,
            system_instructions=dynamic_system_instruction,
            use_global_instructions=True
        )


def generate_unique_id():
    """Function to generate unique id with datetime"""
    uid = uuid5(uuid4(), f"{datetime.now()}")
    return uid.hex


def _get_prompt_template_for_prompt(prompt_template_id: str, user: str, is_global: bool = False) -> Optional[PromptTemplate]:
    """
    Retrieves a prompt template for a given prompt template ID.

    Args:
        prompt_template_id (str): The ID of the prompt template to retrieve.
        user (str): The email of the user requesting the prompt template.
        is_global (bool, optional): Whether to look in global templates. Defaults to False.

    Returns:
        Optional[PromptTemplate]: The prompt template if found and user is authorized, None otherwise.
    """
    if not prompt_template_id:
        return None

    try:
        prompt_template_client = FirestoreClient().GlobalInstanceClient(
        ) if is_global else FirestoreClient().InstanceClient(user_email=user)
        prompt_template_service = PromptTemplateService(
            persistence_client=prompt_template_client)
        prompt_template = None

        if is_global:
            prompt_template = prompt_template_service.get_prompt_template_by_id(
                id=prompt_template_id,
                is_global=True
            )
        else:
            prompt_template = prompt_template_service.get_authorized_prompt_template_by_id(
                id=prompt_template_id,
                user_email=user,
            )

        return prompt_template
    except Exception as e:
        logger_error.error(
            f"{workflow} : _get_prompt_template_for_prompt : {str(e)} : {traceback.format_exc()}"
        )
        return None


def _get_session_type_for_prompt(session_type: str = "chat"):
    try:
        return SessionTypeEnum[session_type.upper()]
    except (KeyError, AttributeError):
        return SessionTypeEnum.CHAT


def async_bots(request: Request, session, chat_session_type: str = "chat"):
    """Defines the main back and forth between Client, Server & VertexAI

    Args:
        is_chat (bool, optional): if True this returns for ChatModel. else CodeModel. Defaults to True.
        is_multi (bool, optional): if True this returns for ChatModel with handling for the file upload.
    Returns:
        JSON: Returns a JSON string with the model response text & prompt_id relating to the response.
    """
    email = session["email"]
    request_body: dict = request.get_json()
    (
        user_input,
        session_id,
        firestore_session_id,
        temperature,
        max_output_tokens,
    ) = map(
        lambda x: request_body.get(x, ""),
        [
            "prompt",
            "session_id",
            "firestore_session_id",
            "temperature",
            "max_output_tokens",
        ],
    )

    prompt_template_id: Optional[str] = request_body.get(
        "prompt_template_id", None)
    prompt_template_is_global: bool = request_body.get(
        "prompt_template_is_global", False)

    # Get system_instructions from request
    system_instructions = request_body.get("system_instructions", "")

    prompt_files = request_body.get("prompt_files", [])
    prompt_session_files: List[SessionFile] = [
        SessionFile.from_upload_dict(file) for file in prompt_files
    ]

    if session_id is None or session_id == "null" or session_id == "undefined":
        session_id = f"{chat_session_type}{generate_unique_id()}"
    try:
        temperature = float(temperature)
        max_output_tokens = int(max_output_tokens)
    except:
        logger_error.error(
            f"{workflow} : bots : Failed to convert option values, using defaults USER: {email}, SESSION: {session_id}"
        )
        temperature = ModelTemperatures.DEFAULT_TEMPERATURE.value
        max_output_tokens = 8192
    params = {
        "temperature": temperature,
        "max_output_tokens": max_output_tokens,
    }

    session_client = FirestoreClient().InstanceClient(user_email=email)
    session_service = SessionService(session_client)
    session_document: Session = None
    try:
        is_new_session = firestore_session_id is None or firestore_session_id.lower() in [
            "null", "undefined"]
        if is_new_session:
            prompt_template = _get_prompt_template_for_prompt(
                prompt_template_id, email, prompt_template_is_global)
            session_type = prompt_template.session_type if prompt_template else _get_session_type_for_prompt(
                chat_session_type)

            # System Instructions Priority Logic:
            # 1. Use user-provided system_instructions from request (highest priority)
            # 2. Fall back to prompt_template system_instructions if no user input
            # 3. Fall back to None if neither is available
            final_system_instructions = None
            if system_instructions:  # User provided custom instructions from frontend
                final_system_instructions = system_instructions
            elif prompt_template and prompt_template.system_instructions:  # Fall back to template
                final_system_instructions = prompt_template.system_instructions

            new_session = CreateSession(
                user_email=session["email"],
                model=CreateGenerativeModel.session_type_to_model_name(
                    chat_session_type
                ),
                session_type=session_type,
                temperature=temperature,
                system_instructions=final_system_instructions,
            )
            session_document = session_service.create_new_session(new_session)
        else:
            session_document = session_service.get_user_session_by_id(
                session_id=firestore_session_id, user_email=email, include_messages=True
            )

            # Update session settings if they have changed
            session_needs_update = False
            update_fields = {}

            # Check if system_instructions need updating
            current_instructions = session_document.system_instructions or ""
            if system_instructions != current_instructions:
                update_fields["system_instructions"] = system_instructions
                session_needs_update = True

            # Update session in Firestore if needed
            if session_needs_update:
                try:
                    update_fields["updated_utc"] = datetime.now(timezone.utc)
                    session_ref = session_client.collection(
                        "Sessions").document(firestore_session_id)
                    session_ref.update(update_fields)

                    # Update the in-memory session object
                    session_document.system_instructions = system_instructions

                    logger_info.info(
                        f"{workflow} : async_bots : Updated session {firestore_session_id} settings for user {email}: {update_fields}"
                    )
                except Exception as e:
                    logger_error.error(
                        f"{workflow} : async_bots : Failed to update session settings: {str(e)}"
                    )

        created_prompt = CreateSessionMessage(
            conversation_role="user", temperature=temperature, message=user_input
        )
        added_prompt = session_service.add_message(
            session_document.id, created_prompt)
        session_document.conversation_history.append(added_prompt)
        if prompt_session_files:
            session_service.add_files(session_document, prompt_session_files)
    except FirestoreLimitException as fle:  # TODO review if these are needed
        exceeded_limit_key = fle.exceeded_limit_name
        exceeded_limit_value = fle.exceeded_limit_value
        logger_error.error(
            f"{workflow} : async_bots : Failure in persisting session_document: {exceeded_limit_key} was exceeded. Value: {exceeded_limit_value}"
        )
        error_response = {
            "message": f"This Sidekick Session is too large and cannot be continued. Please try again with a smaller prompt or start a new session.",
            "firestore_session_id": session_document.id if session_document else None,
        }
        return error_response, 500
    except InvalidArgument as iae:
        logger_error.error(
            f"{workflow} : async_bots : Failure in persisting session_document: {str(iae)}"
        )
        error_response = {
            "message": str(iae),
            "firestore_session_id": session_document.id if session_document else None,
        }
        return error_response, 500

    logger_info.info(
        f"{workflow} : bots : {chat_session_type} session: {datetime.now(TZ)}, USER: {email}, SESSION: {firestore_session_id}, PARAMETERS: {params}"
    )
    bq_index = len(session_document.conversation_history) - 1
    bq_prompt_id = f"{session_document.id}-{bq_index}"
    # TODO double check that len(conversation_history) is correct
    try:
        prompt_as_bq_dict = added_prompt.to_pubsub_message(
            session_document.id,
            email,
            session_document.session_type,
            bq_prompt_id,
        )
        db_utils.add_BQ_session_prompt(prompt_as_bq_dict)
    except ValueError as ve:
        logger_error.error(
            f"{workflow} : add_BQ_session_prompt : {str(ve)} : {traceback.format_exc()}"
        )

    async_message_kwargs = {
        "prompt": added_prompt,
        "session_document": session_document,
        "email": email,
        "params": params,
        "system_instructions": system_instructions,
    }
    thread = threading.Thread(
        target=async_message_wrapper, kwargs=async_message_kwargs)
    thread.start()
    # TODO: Response here needs to be formatted for frontend polling
    insta_response = {
        "response": f"Check history for response: [/sidekick/history/{session_id}](/sidekick/history/{session_id}). Firestore session: {session_document.id}",
        "response_id": f"{added_prompt.id}-{bq_index}",
        "session_id": session_id,
        "firestore_session_id": session_document.id,
        "response_success": True,
    }

    return insta_response, 200


def async_message_wrapper(
    prompt: SessionMessage,
    session_document: Session,
    email: str,
    params: dict,
    system_instructions: str = "",
):
    ################
    chat_history = session_to_gemini_history(
        session=session_document, to_dict=True)
    file_history = session_to_file_history(
        session=session_document, to_dict=True)
    ################
    temperature = params["temperature"]
    session_id = session_document.id

    user_dict = {  # This is for error logging, unrelated to data models.
        "email": email,
        "session_id": session_document.id,
        "temperature": temperature,
        "mime_and_size": log_helper.extract_mime_and_size(file_history),
        "tokens": -1,  # Will be filled during count tokens API call.
        "error": "",
    }

    logger_info.info(
        f"{workflow} : async_message_wrapper : Size of history session_id = {session_document.id} in bytes :{sys.getsizeof(chat_history, 0)}"
    )
    # Send Message
    st = timings.start_timer_log()
    # chat, user_input, params, file_history, user_dict, email
    model_name = CreateGenerativeModel.session_type_to_model_name(
        session_document.session_type)

    # System Instructions Strategy:
    # - Default mode: Use global system instruction from env (when no custom instruction provided)
    # - Custom mode: Use only custom instruction, completely replace global instruction
    # Note: system_instructions here contains the final resolved instructions from either:
    #   1. User's custom instructions (from frontend ChatSettingsModal), or
    #   2. Prompt template instructions (if using a template), or
    #   3. Empty string (will use global instructions as fallback)

    model = _create_model_with_system_instructions(
        model_name=model_name,
        email=email,
        custom_system_instructions=system_instructions
    )
    response = model.chat_response(
        prompt=prompt.message,
        email=email,
        user_dict=user_dict,
        temperature=temperature,
        file_history=file_history,
        conversation_history=chat_history,
    )
    timings.end_timer_log(f"API call USER: {email}, SESSION: {session_id}", st)
    bq_index = len(session_document.conversation_history) - \
        1 if session_document.conversation_history else 0
    firestore_prompt_id = f"{session_document.id}-{bq_index}"
    api_response = {
        "response": response[1],
        "response_id": firestore_prompt_id,
        "session_id": session_id,
        "firestore_session_id": session_document.id,
        "response_success": response[0],
    }

    client = FirestoreClient().InstanceClient(user_email=email)
    session_service = SessionService(client)
    session_document = session_service.get_user_session_by_id(
        session_id=session_id, user_email=email, include_messages=True
    )
    try:
        created_response = CreateSessionMessage(
            conversation_role="model",
            temperature=temperature,
            message=response[1],
        )
        added_response = session_service.add_message(
            session_document.id, created_response
        )
    except FirestoreLimitException as fle:  # TODO review if these are needed
        logger_error.error(
            f"{workflow} : async_message_wrapper : Failure in persisting session_document: {fle.exceeded_limit_name} was exceeded. Value: {fle.exceeded_limit_value}"
        )
        error_response = {
            "message": f"The response for your prompt would make the Sidekick Session too large and cannot be continued. Please start a new session.",
            "firestore_session_id": (session_document.id if session_document else None),
        }
        created_response = CreateSessionMessage(
            conversation_role="model",
            temperature=temperature,
            message=error_response["message"],
        )
        added_response = session_service.add_message(
            session_document.id, created_response
        )
        return error_response, 500
    except InvalidArgument as iae:
        logger_error.error(
            f"{workflow} : async_message_wrapper : Failure in persisting session_document: {str(iae)}"
        )
        error_response = {
            "message": str(iae),
            "firestore_session_id": (session_document.id if session_document else None),
        }
        created_response = CreateSessionMessage(
            conversation_role="model",
            temperature=temperature,
            message=error_response["message"],
        )
        added_response = session_service.add_message(
            session_document.id, created_response
        )
        return error_response, 500
    try:
        bq_response_id = f"{session_document.id}-{bq_index}"
        prompt_response_as_bq_dict = added_response.to_pubsub_message(
            session_document.id, email, session_document.session_type, bq_response_id
        )
        db_utils.add_BQ_session_prompt_response(prompt_response_as_bq_dict)
    except ValueError as ve:
        logger_error.error(
            f"{workflow} : add_BQ_session_prompt_response : {str(ve)} : {traceback.format_exc()}"
        )

    return api_response, 200
